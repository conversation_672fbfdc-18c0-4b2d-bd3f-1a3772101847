import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  int? _resendToken;

  /// Verifies the phone number and sends OTP
  Future<void> verifyPhoneNumber(
    String phoneNumber,
    Function(String, int?) codeSent,
  ) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      timeout: const Duration(seconds: 60),
      forceResendingToken: _resendToken,

      /// Auto-verification when OTP is detected automatically (e.g., Android)
      verificationCompleted: (PhoneAuthCredential credential) async {
        try {
          await _auth.signInWithCredential(credential);
          log("Auto verification completed");
        } catch (e) {
          log("Auto verification error: $e");
        }
      },

      /// If verification fails (e.g., invalid number)
      verificationFailed: (FirebaseAuthException e) {
        log("Verification failed: ${e.message}");
      },

      /// When the OTP is sent, the verificationId is required for signing in
      codeSent: (String verificationId, int? resendToken) {
        _resendToken = resendToken;
        codeSent(verificationId, resendToken);
        log("OTP sent to $phoneNumber");
      },

      /// If the OTP is not auto-retrieved within the timeout
      codeAutoRetrievalTimeout: (String verificationId) {
        log("Auto retrieval timeout");
      },
    );
  }

  /// Signs in the user with the OTP
  Future<UserCredential?> signInWithOTP(
    String verificationId,
    String smsCode,
  ) async {
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );

      UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );
      log("User signed in: ${userCredential.user?.uid}");

      return userCredential;
    } catch (e) {
      log("Sign-in error: $e");
      return null;
    }
  }

  Future<void> logout() async {
    await _auth.signOut();

    log("User logged out successfully");
  }
}
