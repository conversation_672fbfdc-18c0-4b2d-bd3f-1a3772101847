import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:packagingwala/models/customer.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get customer by mobile number
  Future<Customer?> getCustomerByMobileNumber(String mobileNumber) async {
    try {
      log('Searching for customer with mobile: $mobileNumber');

      // Create different format variations of the phone number
      List<String> phoneVariations = _generatePhoneVariations(mobileNumber);

      // Try each variation
      for (String phoneVariation in phoneVariations) {
        log('Trying phone format: $phoneVariation');

        final querySnapshot = await _firestore
            .collection('customers')
            .where('mobileNumber', isEqualTo: phoneVariation)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final doc = querySnapshot.docs.first;
          final customer = Customer.fromFirestore(doc.data());
          log('Customer found with format "$phoneVariation": ${customer.fullName}');
          return customer;
        }
      }

      log('No customer found with any mobile format variations: $phoneVariations');
      return null;
    } catch (e) {
      log('Error fetching customer: $e');
      return null;
    }
  }

  /// Generate different phone number format variations
  List<String> _generatePhoneVariations(String phoneNumber) {
    List<String> variations = [];

    // Remove all spaces and special characters except +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Add the original number
    variations.add(phoneNumber);

    if (cleanNumber.startsWith('+91')) {
      String numberPart = cleanNumber.substring(3); // Remove +91

      // Different variations:
      variations.addAll([
        cleanNumber,                    // +919001136688
        '+91 $numberPart',             // +91 9001136688
        '+91-$numberPart',             // +91-9001136688
        '+91 ${numberPart.substring(0, 5)} ${numberPart.substring(5)}', // +91 90011 36688
        numberPart,                    // 9001136688
        '91$numberPart',              // 919001136688
      ]);
    }

    // Remove duplicates
    variations = variations.toSet().toList();

    log('Generated phone variations: $variations');
    return variations;
  }

  /// Get customer by customer ID
  Future<Customer?> getCustomerById(String customerId) async {
    try {
      log('Fetching customer with ID: $customerId');

      final docSnapshot = await _firestore
          .collection('customers')
          .doc(customerId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final customer = Customer.fromFirestore(docSnapshot.data()!);
        log('Customer found: ${customer.fullName}');
        return customer;
      } else {
        log('No customer found with ID: $customerId');
        return null;
      }
    } catch (e) {
      log('Error fetching customer by ID: $e');
      return null;
    }
  }

  /// Update customer data
  Future<bool> updateCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .update(customer.toFirestore());

      log('Customer updated successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error updating customer: $e');
      return false;
    }
  }

  /// Create new customer
  Future<bool> createCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .set(customer.toFirestore());

      log('Customer created successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error creating customer: $e');
      return false;
    }
  }
}
