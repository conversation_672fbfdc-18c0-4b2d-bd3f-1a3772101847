import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:packagingwala/models/customer.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get customer by mobile number
  Future<Customer?> getCustomerByMobileNumber(String mobileNumber) async {
    try {
      log('Searching for customer with mobile: $mobileNumber');
      
      // Query customers collection where mobileNumber matches
      final querySnapshot = await _firestore
          .collection('customers')
          .where('mobileNumber', isEqualTo: mobileNumber)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final customer = Customer.fromFirestore(doc.data());
        log('Customer found: ${customer.fullName}');
        return customer;
      } else {
        log('No customer found with mobile: $mobileNumber');
        return null;
      }
    } catch (e) {
      log('Error fetching customer: $e');
      return null;
    }
  }

  /// Get customer by customer ID
  Future<Customer?> getCustomerById(String customerId) async {
    try {
      log('Fetching customer with ID: $customerId');
      
      final docSnapshot = await _firestore
          .collection('customers')
          .doc(customerId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final customer = Customer.fromFirestore(docSnapshot.data()!);
        log('Customer found: ${customer.fullName}');
        return customer;
      } else {
        log('No customer found with ID: $customerId');
        return null;
      }
    } catch (e) {
      log('Error fetching customer by ID: $e');
      return null;
    }
  }

  /// Update customer data
  Future<bool> updateCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .update(customer.toFirestore());
      
      log('Customer updated successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error updating customer: $e');
      return false;
    }
  }

  /// Create new customer
  Future<bool> createCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .set(customer.toFirestore());
      
      log('Customer created successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error creating customer: $e');
      return false;
    }
  }
}
