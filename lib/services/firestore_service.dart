import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:packagingwala/models/customer.dart';
import 'package:packagingwala/models/order.dart' as order_model;

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get customer by mobile number
  Future<Customer?> getCustomerByMobileNumber(String mobileNumber) async {
    try {
      log('Searching for customer with mobile: $mobileNumber');

      // Create different format variations of the phone number
      List<String> phoneVariations = _generatePhoneVariations(mobileNumber);

      // Try each variation
      for (String phoneVariation in phoneVariations) {
        log('Trying phone format: $phoneVariation');

        final querySnapshot = await _firestore
            .collection('customers')
            .where('mobileNumber', isEqualTo: phoneVariation)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final doc = querySnapshot.docs.first;
          final customer = Customer.fromFirestore(doc.data());
          log('Customer found with format "$phoneVariation": ${customer.fullName}');
          return customer;
        }
      }

      log('No customer found with any mobile format variations: $phoneVariations');
      return null;
    } catch (e) {
      log('Error fetching customer: $e');
      return null;
    }
  }

  /// Generate different phone number format variations
  List<String> _generatePhoneVariations(String phoneNumber) {
    List<String> variations = [];

    // Remove all spaces and special characters except +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Add the original number
    variations.add(phoneNumber);

    if (cleanNumber.startsWith('+91')) {
      String numberPart = cleanNumber.substring(3); // Remove +91

      // Different variations:
      variations.addAll([
        cleanNumber,                    // +919001136688
        '+91 $numberPart',             // +91 9001136688
        '+91-$numberPart',             // +91-9001136688
        '+91 ${numberPart.substring(0, 5)} ${numberPart.substring(5)}', // +91 90011 36688
        numberPart,                    // 9001136688
        '91$numberPart',              // 919001136688
      ]);
    }

    // Remove duplicates
    variations = variations.toSet().toList();

    log('Generated phone variations: $variations');
    return variations;
  }

  /// Get customer by customer ID
  Future<Customer?> getCustomerById(String customerId) async {
    try {
      log('Fetching customer with ID: $customerId');

      final docSnapshot = await _firestore
          .collection('customers')
          .doc(customerId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final customer = Customer.fromFirestore(docSnapshot.data()!);
        log('Customer found: ${customer.fullName}');
        return customer;
      } else {
        log('No customer found with ID: $customerId');
        return null;
      }
    } catch (e) {
      log('Error fetching customer by ID: $e');
      return null;
    }
  }

  /// Update customer data
  Future<bool> updateCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .update(customer.toFirestore());

      log('Customer updated successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error updating customer: $e');
      return false;
    }
  }

  /// Create new customer
  Future<bool> createCustomer(Customer customer) async {
    try {
      await _firestore
          .collection('customers')
          .doc(customer.customerId)
          .set(customer.toFirestore());

      log('Customer created successfully: ${customer.customerId}');
      return true;
    } catch (e) {
      log('Error creating customer: $e');
      return false;
    }
  }

  /// Get orders by customer ID
  Future<List<order_model.Order>> getOrdersByCustomerId(String customerId) async {
    try {
      log('Fetching orders for customer ID: $customerId');

      final querySnapshot = await _firestore
          .collection('orders')
          .where('customerId', isEqualTo: customerId)
          .get();

      List<order_model.Order> orders = [];
      for (var doc in querySnapshot.docs) {
        try {
          final order = order_model.Order.fromFirestore(doc.data());
          orders.add(order);
        } catch (e) {
          log('Error parsing order ${doc.id}: $e');
        }
      }

      // Sort orders by creation date in memory (newest first)
      orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      log('Found ${orders.length} orders for customer: $customerId');
      return orders;
    } catch (e) {
      log('Error fetching orders: $e');
      return [];
    }
  }

  /// Get all orders (for admin view)
  Future<List<order_model.Order>> getAllOrders() async {
    try {
      log('Fetching all orders');

      final querySnapshot = await _firestore
          .collection('orders')
          .get();

      List<order_model.Order> orders = [];
      for (var doc in querySnapshot.docs) {
        try {
          final order = order_model.Order.fromFirestore(doc.data());
          orders.add(order);
        } catch (e) {
          log('Error parsing order ${doc.id}: $e');
        }
      }

      // Sort orders by creation date in memory (newest first)
      orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      log('Found ${orders.length} total orders');
      return orders;
    } catch (e) {
      log('Error fetching all orders: $e');
      return [];
    }
  }

  /// Get order by order ID
  Future<order_model.Order?> getOrderById(String orderId) async {
    try {
      log('Fetching order with ID: $orderId');

      final docSnapshot = await _firestore
          .collection('orders')
          .doc(orderId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final order = order_model.Order.fromFirestore(docSnapshot.data()!);
        log('Order found: ${order.orderId}');
        return order;
      } else {
        log('No order found with ID: $orderId');
        return null;
      }
    } catch (e) {
      log('Error fetching order by ID: $e');
      return null;
    }
  }

  /// Update order data
  Future<bool> updateOrder(order_model.Order order) async {
    try {
      await _firestore
          .collection('orders')
          .doc(order.orderId)
          .update(order.toFirestore());

      log('Order updated successfully: ${order.orderId}');
      return true;
    } catch (e) {
      log('Error updating order: $e');
      return false;
    }
  }

  /// Create new order
  Future<bool> createOrder(order_model.Order order) async {
    try {
      await _firestore
          .collection('orders')
          .doc(order.orderId)
          .set(order.toFirestore());

      log('Order created successfully: ${order.orderId}');
      return true;
    } catch (e) {
      log('Error creating order: $e');
      return false;
    }
  }
}
