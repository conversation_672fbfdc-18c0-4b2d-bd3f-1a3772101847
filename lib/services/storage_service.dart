import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userPhoneKey = 'user_phone';
  static const String _userUidKey = 'user_uid';

  static SharedPreferences? _prefs;

  /// Initialize SharedPreferences
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    log('StorageService initialized');
  }

  /// Get SharedPreferences instance
  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call StorageService.init() first.');
    }
    return _prefs!;
  }

  /// Save login state
  static Future<bool> setLoggedIn(bool isLoggedIn) async {
    try {
      final result = await prefs.setBool(_isLoggedInKey, isLoggedIn);
      log('Login state saved: $isLoggedIn');
      return result;
    } catch (e) {
      log('Error saving login state: $e');
      return false;
    }
  }

  /// Get login state
  static bool isLoggedIn() {
    try {
      final result = prefs.getBool(_isLoggedInKey) ?? false;
      log('Login state retrieved: $result');
      return result;
    } catch (e) {
      log('Error retrieving login state: $e');
      return false;
    }
  }

  /// Save user phone number
  static Future<bool> setUserPhone(String phone) async {
    try {
      final result = await prefs.setString(_userPhoneKey, phone);
      log('User phone saved: $phone');
      return result;
    } catch (e) {
      log('Error saving user phone: $e');
      return false;
    }
  }

  /// Get user phone number
  static String? getUserPhone() {
    try {
      final result = prefs.getString(_userPhoneKey);
      log('User phone retrieved: $result');
      return result;
    } catch (e) {
      log('Error retrieving user phone: $e');
      return null;
    }
  }

  /// Save user UID
  static Future<bool> setUserUid(String uid) async {
    try {
      final result = await prefs.setString(_userUidKey, uid);
      log('User UID saved: $uid');
      return result;
    } catch (e) {
      log('Error saving user UID: $e');
      return false;
    }
  }

  /// Get user UID
  static String? getUserUid() {
    try {
      final result = prefs.getString(_userUidKey);
      log('User UID retrieved: $result');
      return result;
    } catch (e) {
      log('Error retrieving user UID: $e');
      return null;
    }
  }

  /// Save a string value
  static Future<bool> setString(String key, String value) async {
    try {
      final result = await prefs.setString(key, value);
      log('String saved: $key = $value');
      return result;
    } catch (e) {
      log('Error saving string: $e');
      return false;
    }
  }

  /// Get a string value
  static String? getString(String key) {
    try {
      final result = prefs.getString(key);
      log('String retrieved: $key = $result');
      return result;
    } catch (e) {
      log('Error retrieving string: $e');
      return null;
    }
  }

  /// Clear all user data
  static Future<bool> clearUserData() async {
    try {
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_userPhoneKey);
      await prefs.remove(_userUidKey);
      await prefs.remove('customer_id');
      log('User data cleared');
      return true;
    } catch (e) {
      log('Error clearing user data: $e');
      return false;
    }
  }
}
