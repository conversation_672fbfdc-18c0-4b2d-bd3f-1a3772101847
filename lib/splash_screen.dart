import 'package:flutter/material.dart';
import 'package:packagingwala/screens/home/<USER>';
import 'package:packagingwala/screens/login/animated_auth_screen.dart';
import 'package:packagingwala/services/storage_service.dart';
import 'package:packagingwala/constants/app_colors.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    // Wait for a short delay to show splash screen
    await Future.delayed(const Duration(seconds: 2));

    // Check if user is logged in
    final isLoggedIn = StorageService.isLoggedIn();

    if (mounted) {
      if (isLoggedIn) {
        // Navigate to home screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      } else {
        // Navigate to login screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const AnimatedAuthScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryColor.withValues(alpha: 0.1),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Image.asset(
                'assets/images/brand_logo.png',
                width: 80,
                height: 80,
              ),
            ),

            const SizedBox(height: 20),

            // App name
            Image.asset(
              'assets/images/brand_logo_text.png',
              width: 180,
            ),

            const SizedBox(height: 10),

            Text(
              'Your Packaging Partner',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 40),

            // Loading indicator
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
            ),
          ],
        ),
      ),
    );
  }
}
