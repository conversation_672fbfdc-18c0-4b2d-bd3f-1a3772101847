import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/providers/splash_provider.dart';
import 'package:packagingwala/screens/home/<USER>';
import 'package:packagingwala/screens/login/animated_auth_screen.dart';
import '../../constants/app_colors.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Listen to splash state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen<SplashState>(splashControllerProvider, (previous, next) {
        if (mounted) {
          switch (next.status) {
            case SplashStatus.navigateToHome:
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const HomeScreen()),
              );
              break;
            case SplashStatus.navigateToLogin:
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const AnimatedAuthScreen()),
              );
              break;
            case SplashStatus.loading:
              // Stay on splash screen
              break;
          }
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final splashState = ref.watch(splashControllerProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Centered logo and text
            Column(
              children: [
                // Circular background with logo
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryColor.withValues(alpha: 0.1),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Image.asset(
                    'assets/images/brand_logo.png',
                    width: 80,
                    height: 80,
                  ),
                ),
                const SizedBox(height: 20),

                // Logo text
                Image.asset('assets/images/brand_logo_text.png', width: 180),

                const SizedBox(height: 10),

                // Subtitle
                Text(
                  'Your Packaging Partner',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),

                const SizedBox(height: 40),

                // Loading indicator
                if (splashState.status == SplashStatus.loading)
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
