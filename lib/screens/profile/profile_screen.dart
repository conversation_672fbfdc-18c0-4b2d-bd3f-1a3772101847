import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:packagingwala/screens/profile/help_center_screen.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/widgets/platform_icon.dart';
import 'package:packagingwala/widgets/smart_svg_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:permission_handler/permission_handler.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _notificationEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkNotificationPermission();
  }

  Future<void> _checkNotificationPermission() async {
    final status = await Permission.notification.status;
    log('Notification permission status: $status');
    setState(() {
      _notificationEnabled = status.isGranted;
    });
  }

  Future<void> _toggleNotification(bool value) async {
    if (value) {
      final status = await Permission.notification.request();
      setState(() {
        _notificationEnabled = status.isGranted;
      });
    } else {
      // Can't programmatically disable notifications, show dialog to go to settings
      _showNotificationSettingsDialog();
    }
  }

  void _showNotificationSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Settings'),
          content: const Text(
            'To disable notifications, please go to your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(title: 'My Account', showBackButton: true),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  children: [
                    // Profile Header
                    _buildProfileHeader(),

                    Space.height(24),

                    // Menu Items
                    _profileSettings(),
                  ],
                ),
              ),
            ),

            // Logout Button at bottom
            _buildLogoutButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size8,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Picture
          Container(
            width: MySize.size60,
            height: MySize.size60,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: PlatformIcon(
                iconName: 'person',
                size: MySize.size30,
                color: Colors.white,
              ),
            ),
          ),

          Space.width(16),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Milan Goyal',
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),

                Space.height(4),

                Text(
                  '+91 9001136688',
                  style: TextStyle(fontSize: MySize.size14, color: AppColors.greyColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _profileSettings() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: MySize.size20, vertical: MySize.size12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size8,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            svgIcon: 'assets/icons/notification_icon.svg',
            title: 'Notification settings',
            hasToggle: true,
            onTap: () {},
          ),
          Divider(height: MySize.size1, thickness: 0.5, color: Colors.grey),
          _buildMenuItem(
            svgIcon: 'assets/icons/privacy_policy_icon.svg',
            title: 'Privacy Policy',
            onTap: () {},
          ),
          Divider(height: MySize.size1, thickness: 0.4, color: Colors.grey),
          _buildMenuItem(
            svgIcon: 'assets/icons/support_icon.svg',
            title: 'Help Center',
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => const HelpCenterScreen()));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    String? icon,
    String? svgIcon,
    required String title,
    bool hasToggle = false,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: MySize.size12),
      child: InkWell(
        onTap: hasToggle ? null : onTap,
        child: Row(
          children: [
            svgIcon != null
                ? SmartIcon.grey(
                  assetPath: svgIcon,
                  size: MySize.size24,
                )
                : PlatformIcon(
                  iconName: icon!,
                  size: MySize.size20,
                  color: AppColors.greyColor,
                ),
            Space.width(16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.blackColor,
                ),
              ),
            ),
            if (hasToggle)
              Transform.scale(
                scale: 0.75,
                child: Switch(
                  value: _notificationEnabled,
                  onChanged: _toggleNotification,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              )
            else
              PlatformIcon(
                iconName: 'forward',
                size: MySize.size16,
                color: AppColors.greyColor,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size16),
      child: OutlinedButton(
        onPressed: () {
          // Show logout confirmation dialog
          _showLogoutDialog();
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.red,
          side: BorderSide(color: Colors.red.shade300),
          padding: EdgeInsets.symmetric(vertical: MySize.size16),
          shape: Shape.circular(12, shapeTypeFor: ShapeTypeFor.button),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.logout, size: MySize.size20),
            Space.width(8),
            Text(
              'Log out',
              style: TextStyle(fontSize: MySize.size16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
