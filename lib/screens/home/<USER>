import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/providers/customer_provider.dart';
import 'package:packagingwala/providers/orders_provider.dart';
import 'package:packagingwala/models/order.dart';
import 'package:packagingwala/widgets/custom_text_field.dart';
import 'package:packagingwala/widgets/platform_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';

import 'package:packagingwala/screens/orders/order_details_screen.dart';
import 'package:packagingwala/screens/profile/profile_screen.dart';
import 'package:packagingwala/screens/notifications/notifications_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int selectedTabIndex = 0;
  String searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  final List<String> tabs = [
    'All Orders',
    'Pending Orders',
    'Processed Orders',
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Space.height(16),

              // Profile and Notification
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ProfileScreen(),
                        ),
                      );
                    },
                    child: CircleAvatar(
                      radius: MySize.size28,
                      backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
                      child: PlatformIcon(
                        iconName: 'person',
                        size: MySize.size32,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                  Space.width(12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Consumer(
                        builder: (context, ref, child) {
                          final customerState = ref.watch(customerControllerProvider);
                          final displayName = customerState.customer?.fullName.isNotEmpty == true
                              ? customerState.customer!.fullName
                              : customerState.customer?.businessName ?? "User";
                          return Text(
                            "Hi $displayName",
                            style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                      ),
                      Row(
                        children: [
                          PlatformIcon(
                            iconName: 'location',
                            size: MySize.size14,
                            color: AppColors.primaryColor,
                          ),
                          Space.width(4),
                          Consumer(
                            builder: (context, ref, child) {
                              final customerState = ref.watch(customerControllerProvider);
                              final address = customerState.customer?.businessAddress ?? "Delhi";
                              return Text(
                                address,
                                style: TextStyle(fontSize: MySize.size14, color: AppColors.greyColor),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: Shape.circular(12),
                      boxShadow: [
                        BoxShadow(
                          blurRadius: MySize.size6,
                          color: Colors.black.withValues(alpha: 0.05),
                          offset: Offset(0, MySize.size2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: const PlatformIcon(
                        iconName: 'notifications',
                        color: AppColors.primaryColor,
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const NotificationsScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),

              Space.height(20),

              // Search Box
              CustomTextField(
                controller: _searchController,
                hintText: "Search Orders",
                prefixIcon: const PlatformIcon(
                  iconName: 'search',
                  color: AppColors.primaryColor,
                ),
                fillColor: Colors.white,
                borderColor: AppColors.primaryColor,
                focusedBorderColor: AppColors.primaryColor,
                borderRadius: MySize.size12,
                contentPadding: EdgeInsets.symmetric(vertical: MySize.size12),
                onChanged: (value) {
                  setState(() {
                    searchQuery = value;
                  });
                },
              ),

              Space.height(20),

              // Tabs
              SizedBox(
                height: MySize.size40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: tabs.length,
                  itemBuilder: (context, index) {
                    return _buildTab(
                      tabs[index],
                      isSelected: selectedTabIndex == index,
                      onTap: () {
                        setState(() {
                          selectedTabIndex = index;
                        });
                      },
                    );
                  },
                ),
              ),

              Space.height(20),

              // Orders Grid
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final customerState = ref.watch(customerControllerProvider);
                    final ordersState = ref.watch(ordersProvider);

                    // Check if customer is loaded first
                    if (customerState.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primaryColor,
                        ),
                      );
                    }

                    if (customerState.customer == null) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const PlatformIcon(
                              iconName: 'error',
                              size: 48,
                              color: AppColors.greyColor,
                            ),
                            Space.height(16),
                            Text(
                              'Customer not found',
                              style: TextStyle(
                                fontSize: MySize.size16,
                                color: AppColors.greyColor,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    // Customer is loaded, now load orders if not already loaded
                    if (ordersState.allOrders.isEmpty && !ordersState.isLoading && ordersState.error == null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        ref.read(ordersProvider.notifier).loadCustomerOrders();
                      });
                    }

                    if (ordersState.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primaryColor,
                        ),
                      );
                    }

                    if (ordersState.error != null) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const PlatformIcon(
                              iconName: 'error',
                              size: 48,
                              color: AppColors.greyColor,
                            ),
                            Space.height(16),
                            Text(
                              'Failed to load orders',
                              style: TextStyle(
                                fontSize: MySize.size16,
                                color: AppColors.greyColor,
                              ),
                            ),
                            Space.height(8),
                            TextButton(
                              onPressed: () {
                                ref.read(ordersProvider.notifier).refreshOrders();
                              },
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    }

                    // Get filtered orders based on search and tab
                    final ordersNotifier = ref.read(ordersProvider.notifier);
                    final filteredOrders = ordersNotifier.searchOrders(searchQuery, selectedTabIndex);

                    if (filteredOrders.isEmpty) {
                      return RefreshIndicator(
                        onRefresh: () async {
                          await ref.read(ordersProvider.notifier).refreshOrders();
                        },
                        color: AppColors.primaryColor,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: MediaQuery.of(context).size.height * 0.5,
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const PlatformIcon(
                                    iconName: 'inbox',
                                    size: 48,
                                    color: AppColors.greyColor,
                                  ),
                                  Space.height(16),
                                  Text(
                                    searchQuery.isNotEmpty
                                      ? 'No orders found for "$searchQuery"'
                                      : 'No orders found',
                                    style: TextStyle(
                                      fontSize: MySize.size16,
                                      color: AppColors.greyColor,
                                    ),
                                  ),
                                  Space.height(8),
                                  Text(
                                    'Pull down to refresh',
                                    style: TextStyle(
                                      fontSize: MySize.size12,
                                      color: AppColors.greyColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        await ref.read(ordersProvider.notifier).refreshOrders();
                      },
                      color: AppColors.primaryColor,
                      child: GridView.builder(
                        physics: const AlwaysScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: MySize.size12,
                          mainAxisSpacing: MySize.size12,
                          childAspectRatio: 0.7,
                        ),
                        itemCount: filteredOrders.length,
                        itemBuilder: (context, index) {
                          return _buildOrderCard(filteredOrders[index]);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(
    String label, {
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: MySize.size16, vertical: MySize.size9),
        margin: EdgeInsets.only(right: MySize.size10),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryColor : const Color(0xFFEFEFEF),
          borderRadius: Shape.circular(20),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? AppColors.blackColor : AppColors.greyColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrderDetailsScreen(
              orderId: order.orderId,
              orderDate: order.formattedCreatedDate,
              orderImage: order.hasImage
                ? (order.productImagePath ?? order.productImageUrl ?? 'assets/images/image1.png')
                : 'assets/images/image1.png',
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: Shape.circular(14),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: MySize.size4,
              offset: Offset(0, MySize.size2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: Shape.circularTop(14),
                child: _buildOrderImage(order),
              ),
            ),
            Expanded(
              flex: 1,
              child: Padding(
                padding: EdgeInsets.all(MySize.size8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      'ID: ${order.orderId}',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: MySize.size13,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        PlatformIcon(
                          iconName: 'calendar',
                          size: MySize.size12,
                          color: AppColors.primaryColor,
                        ),
                        Space.width(4),
                        Expanded(
                          child: Text(
                            order.formattedCreatedDate,
                            style: TextStyle(
                              fontSize: MySize.size11,
                              color: AppColors.greyColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderImage(Order order) {
    // Try to load the image from URL first, then path, then fallback to default image
    if (order.productImageUrl != null && order.productImageUrl!.isNotEmpty) {
      return Image.network(
        order.productImageUrl!,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // If network image fails, try asset image or show default
          return _buildFallbackImage(order);
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: AppColors.backgroundColor,
            child: Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryColor,
                strokeWidth: 2,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
      );
    } else if (order.productImagePath != null && order.productImagePath!.isNotEmpty) {
      return Image.asset(
        order.productImagePath!,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultImage();
        },
      );
    } else {
      // No image URL or path, show default image
      return _buildDefaultImage();
    }
  }

  Widget _buildFallbackImage(Order order) {
    // Try asset image first, then default
    if (order.productImagePath != null && order.productImagePath!.isNotEmpty) {
      return Image.asset(
        order.productImagePath!,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultImage();
        },
      );
    } else {
      return _buildDefaultImage();
    }
  }

  Widget _buildDefaultImage() {
    // Show the default image1.png instead of placeholder icon
    return Image.asset(
      'assets/images/image1.png',
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return _buildPlaceholderImage();
      },
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.backgroundColor,
      child: Center(
        child: PlatformIcon(
          iconName: 'image',
          size: MySize.size32,
          color: AppColors.greyColor,
        ),
      ),
    );
  }
}
