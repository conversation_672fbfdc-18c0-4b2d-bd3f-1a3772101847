import 'package:flutter/material.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/widgets/smart_svg_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:packagingwala/screens/orders/order_status_screen.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final String orderDate;
  final String orderImage;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.orderImage,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Order Details',
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Header
                    _buildOrderHeader(),

                    Space.height(24),

                    // Order Tracking
                    _buildOrderTracking(),

                    Space.height(24),

                    // Order Details
                    _buildOrderDetails(),

                    Space.height(24),

                    // Shipping Address
                    _buildShippingAddress(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size4,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: Shape.circular(8),
            child: Image.asset(
              widget.orderImage,
              width: MySize.size60,
              height: MySize.size60,
              fit: BoxFit.cover,
            ),
          ),
          Space.width(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${widget.orderId}',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
                Space.height(4),
                Text(
                  'Placed On ${widget.orderDate}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: Shape.circular(12),
                  ),
                  child: Text(
                    'On Track',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.blackColor
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTracking() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Tracking',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        _buildTrackingStep(
          icon: 'assets/icons/onboarding_icon.svg',
          title: 'On-boarding Process',
          subtitle: '4 June 2025, 10:30 AM',
          description: 'Order items are being Packed',
          isCompleted: true,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/designing_icon.svg',
          title: 'Designing',
          subtitle: '4 June 2025, 11:30 AM',
          description: 'Custom Design Being Created',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/sampling_icon.svg',
          title: 'Sampling',
          subtitle: '4 June 2025, 2:30 PM',
          description: 'Items sorted and organised',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/approval_icon.svg',
          title: 'Design Plate Approval',
          subtitle: '5 June 2025, 9:30 AM',
          description: 'Order entered into production system',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/cylinder_icon.svg',
          title: 'Cylinder Developement',
          subtitle: '5 June 2025, 11:30 AM',
          description: 'Design has been approved for production',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/polyster_approved_icon.svg',
          title: 'Polyster Sample Approved',
          subtitle: '6 June 2025, 8:30 AM',
          description: 'Advanced design modifications',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/polyster_printing_icon.svg',
          title: 'Polyster Printing',
          subtitle: '6 June 2025, 9:40 AM',
          description: 'Advanced design modifications',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/lamination_icon.svg',
          title: 'Lamination',
          subtitle: '6 June 2025, 10:30 AM',
          description: 'Heat treatment process in progress',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/pasting_icon.svg',
          title: 'Metallised Pasting',
          subtitle: '7 June 2025, 2:30 PM',
          description: 'Metallic coating application',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/heating_icon.svg',
          title: 'Heating',
          subtitle: '7 June 2025, 10:30 AM',
          description: 'Order ready for shipping',
          isCompleted: false,
          isActive: false,
        ),

        _buildTrackingStep(
          icon: 'assets/icons/curing_icon.svg',
          title: 'Curing',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Final packaging process',
          isCompleted: false,
          isActive: false,
        ),

        _buildTrackingStep(
          icon: 'assets/icons/zipper_icon.svg',
          title: 'Zipper Addition',
          subtitle: '9 June 2025, 9:30 AM',
          description: 'LD Pasting Application',
          isCompleted: false,
          isActive: false,
        ),

        _buildTrackingStep(
          icon: 'assets/icons/slitting_icon.svg',
          title: 'Slitting',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Polyster printing in progress',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/pouching_icon.svg',
          title: 'Pouching',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Final packaging process',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/sorting_icon.svg',
          title: 'Sorting',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Items getting sorted and organised',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/packing_icon.svg',
          title: 'Packing',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Order items are being packed',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/readytodispatch_icon.svg',
          title: 'Ready to Dispatch',
          subtitle: '8 June 2025, 2:30 PM',
          description: 'Order ready for shipping',
          isCompleted: false,
          isActive: false,
        ),
        _buildTrackingStep(
          icon: 'assets/icons/dispatched_icon.svg',
          title: 'Dispatched',
          subtitle: '9 June 2025, 9:30 AM',
          description: '',
          isCompleted: false,
          isActive: false,
          isLast: true,
        ),
      ],
    );
  }

  Widget _buildTrackingStep({
    required String icon,
    required String title,
    required String subtitle,
    required String description,
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: BoxDecoration(
                color: isCompleted || isActive
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SmartIcon.colored(
                  assetPath: icon,
                  color: isCompleted || isActive ? AppColors.blackColor : AppColors.greyColor,
                  size: MySize.size20,
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: MySize.size2,
                height: MySize.size40,
                color: isCompleted
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        Space.width(12),
        // Content
        Expanded(
          child: Container(
            padding: EdgeInsets.only(bottom: MySize.size16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isActive
                            ? AppColors.blackColor
                            : AppColors.greyColor,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OrderStatusScreen(
                              orderId: widget.orderId,
                              orderDate: widget.orderDate,
                              orderImage: widget.orderImage,
                              stepTitle: title,
                            ),
                          ),
                        );
                      },
                      child: Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                Space.height(4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: Shape.circular(8),
                child: Image.asset(
                  widget.orderImage,
                  width: MySize.size60,
                  height: MySize.size60,
                  fit: BoxFit.cover,
                ),
              ),
              Space.width(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Paper Order',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                    ),
                    Space.height(4),
                    Text(
                      'Quantity: 2 Items',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.greyColor,
                      ),
                    ),
                    Space.height(4),
                    Text(
                      '₹ 450',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShippingAddress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Shipping Address',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Milan Goyal',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
              ),
              Space.height(8),
              Text(
                '1206, 12th Floor, N26, Jaypee Aman, Sector 151, Noida, 201310',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.greyColor,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}