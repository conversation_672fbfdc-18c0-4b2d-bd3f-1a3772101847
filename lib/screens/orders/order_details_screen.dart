import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/widgets/smart_svg_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:packagingwala/screens/orders/order_status_screen.dart';
import 'package:packagingwala/providers/orders_provider.dart';
import 'package:packagingwala/models/order.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;
  final String orderDate;
  final String orderImage;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.orderImage,
  });

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  Order? order;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
  }

  Future<void> _loadOrderDetails() async {
    final loadedOrder = await ref.read(ordersProvider.notifier).getOrderById(widget.orderId);
    if (mounted) {
      setState(() {
        order = loadedOrder;
        isLoading = false;
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Order Details',
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.primaryColor,
                    ),
                  )
                : order == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: AppColors.greyColor,
                          ),
                          Space.height(16),
                          Text(
                            'Order not found',
                            style: TextStyle(
                              fontSize: MySize.size16,
                              color: AppColors.greyColor,
                            ),
                          ),
                        ],
                      ),
                    )
                  : SingleChildScrollView(
                      padding: EdgeInsets.all(MySize.size16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Order Header
                          _buildOrderHeader(),

                          Space.height(24),

                          // Order Tracking
                          _buildOrderTracking(),

                          Space.height(24),

                          // Order Details
                          _buildOrderDetails(),

                          Space.height(24),

                          // Shipping Address
                          _buildShippingAddress(),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    if (order == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size4,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: Shape.circular(8),
            child: _buildOrderImage(),
          ),
          Space.width(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${order!.orderId}',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
                Space.height(4),
                Text(
                  'Placed On ${order!.formattedCreatedDate}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order!.orderStatus),
                    borderRadius: Shape.circular(12),
                  ),
                  child: Text(
                    order!.displayStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.blackColor
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderImage() {
    if (order!.hasImage) {
      if (order!.productImageUrl != null && order!.productImageUrl!.isNotEmpty) {
        return Image.network(
          order!.productImageUrl!,
          width: MySize.size60,
          height: MySize.size60,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackImage();
          },
        );
      } else if (order!.productImagePath != null && order!.productImagePath!.isNotEmpty) {
        return Image.asset(
          order!.productImagePath!,
          width: MySize.size60,
          height: MySize.size60,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackImage();
          },
        );
      }
    }
    return _buildFallbackImage();
  }

  Widget _buildFallbackImage() {
    return Image.asset(
      'assets/images/image1.png',
      width: MySize.size60,
      height: MySize.size60,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: MySize.size60,
          height: MySize.size60,
          color: AppColors.backgroundColor,
          child: Icon(
            Icons.image,
            color: AppColors.greyColor,
            size: MySize.size24,
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange.withValues(alpha: 0.2);
      case 'processing':
      case 'approved':
        return AppColors.primaryColor;
      case 'completed':
        return Colors.green.withValues(alpha: 0.2);
      case 'cancelled':
        return Colors.red.withValues(alpha: 0.2);
      default:
        return AppColors.primaryColor;
    }
  }

  Widget _buildOrderTracking() {
    if (order == null) return const SizedBox.shrink();

    final trackingSteps = _getTrackingSteps();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Tracking',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        ...trackingSteps.map((step) => _buildTrackingStep(
          icon: step['icon'] as String,
          title: step['title'] as String,
          subtitle: step['subtitle'] as String,
          description: step['description'] as String,
          isCompleted: step['isCompleted'] as bool,
          isActive: step['isActive'] as bool,
          isLast: step['isLast'] as bool? ?? false,
        )),
      ],
    );
  }

  List<Map<String, dynamic>> _getTrackingSteps() {
    if (order == null) return [];

    final status = order!.orderStatus.toLowerCase();
    final createdDate = order!.formattedCreatedDate;

    // Define all possible steps
    final allSteps = [
      {
        'icon': 'assets/icons/onboarding_icon.svg',
        'title': 'Order Placed',
        'subtitle': createdDate,
        'description': 'Order has been placed and confirmed',
        'status': 'placed'
      },
      {
        'icon': 'assets/icons/designing_icon.svg',
        'title': 'Designing',
        'subtitle': '',
        'description': 'Custom design being created',
        'status': 'designing'
      },
      {
        'icon': 'assets/icons/sampling_icon.svg',
        'title': 'Sampling',
        'subtitle': '',
        'description': 'Sample preparation in progress',
        'status': 'sampling'
      },
      {
        'icon': 'assets/icons/approval_icon.svg',
        'title': 'Sample Approved',
        'subtitle': '',
        'description': 'Sample has been approved for production',
        'status': 'approved'
      },
      {
        'icon': 'assets/icons/polyster_printing_icon.svg',
        'title': 'Production',
        'subtitle': '',
        'description': 'Order is in production',
        'status': 'production'
      },
      {
        'icon': 'assets/icons/packing_icon.svg',
        'title': 'Packing',
        'subtitle': '',
        'description': 'Order items are being packed',
        'status': 'packing'
      },
      {
        'icon': 'assets/icons/readytodispatch_icon.svg',
        'title': 'Ready to Dispatch',
        'subtitle': '',
        'description': 'Order ready for shipping',
        'status': 'ready'
      },
      {
        'icon': 'assets/icons/dispatched_icon.svg',
        'title': 'Dispatched',
        'subtitle': '',
        'description': 'Order has been dispatched',
        'status': 'dispatched'
      },
    ];

    // Determine current step based on order status
    int currentStepIndex = _getCurrentStepIndex(status);

    // Mark steps as completed, active, or pending
    for (int i = 0; i < allSteps.length; i++) {
      allSteps[i]['isCompleted'] = i < currentStepIndex;
      allSteps[i]['isActive'] = i == currentStepIndex;
      allSteps[i]['isLast'] = i == allSteps.length - 1;

      // Add estimated dates for future steps
      if (i > 0 && allSteps[i]['subtitle'] == '') {
        allSteps[i]['subtitle'] = 'Estimated completion';
      }
    }

    return allSteps;
  }

  int _getCurrentStepIndex(String status) {
    switch (status) {
      case 'pending':
        return 0; // Order Placed
      case 'designing':
        return 1; // Designing
      case 'sampling':
        return 2; // Sampling
      case 'approved':
      case 'polyester sample approved':
        return 3; // Sample Approved
      case 'production':
      case 'printing':
      case 'processing':
        return 4; // Production
      case 'packing':
        return 5; // Packing
      case 'ready':
      case 'ready to dispatch':
        return 6; // Ready to Dispatch
      case 'dispatched':
      case 'completed':
        return 7; // Dispatched
      default:
        return 0; // Default to first step
    }
  }

  Widget _buildTrackingStep({
    required String icon,
    required String title,
    required String subtitle,
    required String description,
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: BoxDecoration(
                color: isCompleted || isActive
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SmartIcon.colored(
                  assetPath: icon,
                  color: isCompleted || isActive ? AppColors.blackColor : AppColors.greyColor,
                  size: MySize.size20,
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: MySize.size2,
                height: MySize.size40,
                color: isCompleted
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        Space.width(12),
        // Content
        Expanded(
          child: Container(
            padding: EdgeInsets.only(bottom: MySize.size16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isActive
                            ? AppColors.blackColor
                            : AppColors.greyColor,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OrderStatusScreen(
                              orderId: widget.orderId,
                              orderDate: widget.orderDate,
                              orderImage: widget.orderImage,
                              stepTitle: title,
                            ),
                          ),
                        );
                      },
                      child: Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                Space.height(4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetails() {
    if (order == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  ClipRRect(
                    borderRadius: Shape.circular(8),
                    child: _buildOrderImage(),
                  ),
                  Space.width(12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order!.productName.isNotEmpty ? order!.productName : 'Custom Order',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.blackColor,
                          ),
                        ),
                        Space.height(4),
                        Text(
                          'Quantity: ${order!.quantity} Items',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.greyColor,
                          ),
                        ),
                        Space.height(4),
                        Text(
                          order!.formattedPrice,
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Space.height(16),
              // Additional order details
              _buildDetailRow('Order ID', order!.orderId),
              _buildDetailRow('Priority', order!.priority),
              _buildDetailRow('Payment Status', order!.payment),
              if (order!.notes.isNotEmpty) _buildDetailRow('Notes', order!.notes),
              _buildDetailRow('Delivery Date', order!.formattedDeliveryDate),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: MySize.size8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: MySize.size100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.greyColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.blackColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingAddress() {
    if (order == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Shipping Address',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                order!.customerName.isNotEmpty ? order!.customerName : 'Customer',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
              ),
              if (order!.businessName.isNotEmpty) ...[
                Space.height(4),
                Text(
                  order!.businessName,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.blackColor,
                  ),
                ),
              ],
              Space.height(8),
              Text(
                order!.businessAddress.isNotEmpty
                  ? order!.businessAddress
                  : 'Address not available',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.greyColor,
                  height: 1.4,
                ),
              ),
              if (order!.mobileNumber.isNotEmpty) ...[
                Space.height(8),
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: MySize.size16,
                      color: AppColors.greyColor,
                    ),
                    Space.width(8),
                    Text(
                      order!.mobileNumber,
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.greyColor,
                      ),
                    ),
                  ],
                ),
              ],
              if (order!.emailId.isNotEmpty) ...[
                Space.height(4),
                Row(
                  children: [
                    Icon(
                      Icons.email,
                      size: MySize.size16,
                      color: AppColors.greyColor,
                    ),
                    Space.width(8),
                    Expanded(
                      child: Text(
                        order!.emailId,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.greyColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              if (order!.gstNumber.isNotEmpty) ...[
                Space.height(8),
                Text(
                  'GST: ${order!.gstNumber}',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}