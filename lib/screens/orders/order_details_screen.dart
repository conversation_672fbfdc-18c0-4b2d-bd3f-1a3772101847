import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/widgets/smart_svg_icon.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:packagingwala/screens/orders/order_status_screen.dart';
import 'package:packagingwala/providers/orders_provider.dart';
import 'package:packagingwala/models/order.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;
  final String orderDate;
  final String orderImage;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.orderImage,
  });

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  Order? order;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
  }

  Future<void> _loadOrderDetails() async {
    final loadedOrder = await ref.read(ordersProvider.notifier).getOrderById(widget.orderId);
    if (mounted) {
      setState(() {
        order = loadedOrder;
        isLoading = false;
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Order Details',
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.primaryColor,
                    ),
                  )
                : order == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: AppColors.greyColor,
                          ),
                          Space.height(16),
                          Text(
                            'Order not found',
                            style: TextStyle(
                              fontSize: MySize.size16,
                              color: AppColors.greyColor,
                            ),
                          ),
                        ],
                      ),
                    )
                  : SingleChildScrollView(
                      padding: EdgeInsets.all(MySize.size16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Order Header
                          _buildOrderHeader(),

                          Space.height(24),

                          // Order Tracking
                          _buildOrderTracking(),

                          Space.height(24),

                          // Order Details
                          _buildOrderDetails(),

                          Space.height(24),

                          // Shipping Address
                          _buildShippingAddress(),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    if (order == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size4,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: Shape.circular(8),
            child: _buildOrderImage(),
          ),
          Space.width(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${order!.orderId}',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
                Space.height(4),
                Text(
                  'Placed On ${order!.formattedCreatedDate}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order!.orderStatus),
                    borderRadius: Shape.circular(12),
                  ),
                  child: Text(
                    order!.displayStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.blackColor
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderImage() {
    if (order!.hasImage) {
      if (order!.productImageUrl != null && order!.productImageUrl!.isNotEmpty) {
        return Image.network(
          order!.productImageUrl!,
          width: MySize.size60,
          height: MySize.size60,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackImage();
          },
        );
      } else if (order!.productImagePath != null && order!.productImagePath!.isNotEmpty) {
        return Image.asset(
          order!.productImagePath!,
          width: MySize.size60,
          height: MySize.size60,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildFallbackImage();
          },
        );
      }
    }
    return _buildFallbackImage();
  }

  Widget _buildFallbackImage() {
    return Image.asset(
      'assets/images/image1.png',
      width: MySize.size60,
      height: MySize.size60,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: MySize.size60,
          height: MySize.size60,
          color: AppColors.backgroundColor,
          child: Icon(
            Icons.image,
            color: AppColors.greyColor,
            size: MySize.size24,
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange.withValues(alpha: 0.2);
      case 'processing':
      case 'approved':
        return AppColors.primaryColor;
      case 'completed':
        return Colors.green.withValues(alpha: 0.2);
      case 'cancelled':
        return Colors.red.withValues(alpha: 0.2);
      default:
        return AppColors.primaryColor;
    }
  }

  Widget _buildOrderTracking() {
    if (order == null) return const SizedBox.shrink();

    final trackingSteps = _getTrackingSteps();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Tracking',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        ...trackingSteps.map((step) => _buildTrackingStep(
          icon: step['icon'] as String,
          title: step['title'] as String,
          subtitle: step['subtitle'] as String,
          description: step['description'] as String,
          isCompleted: step['isCompleted'] as bool,
          isActive: step['isActive'] as bool,
          isLast: step['isLast'] as bool? ?? false,
        )),
      ],
    );
  }

  List<Map<String, dynamic>> _getTrackingSteps() {
    if (order == null) return [];

    final status = order!.orderStatus.toLowerCase();
    final createdDate = order!.formattedCreatedDate;

    // Define all possible steps (keeping ALL original steps)
    final List<Map<String, dynamic>> allSteps = [
      {
        'icon': 'assets/icons/onboarding_icon.svg',
        'title': 'On-boarding Process',
        'subtitle': createdDate,
        'description': 'Order items are being Packed',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/designing_icon.svg',
        'title': 'Designing',
        'subtitle': _getStepDate(1),
        'description': 'Custom Design Being Created',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/sampling_icon.svg',
        'title': 'Sampling',
        'subtitle': _getStepDate(2),
        'description': 'Items sorted and organised',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/approval_icon.svg',
        'title': 'Design Plate Approval',
        'subtitle': _getStepDate(3),
        'description': 'Order entered into production system',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/cylinder_icon.svg',
        'title': 'Cylinder Developement',
        'subtitle': _getStepDate(4),
        'description': 'Design has been approved for production',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/polyster_approved_icon.svg',
        'title': 'Polyester Sample Approved',
        'subtitle': _getStepDate(5),
        'description': 'Advanced design modifications',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/polyster_printing_icon.svg',
        'title': 'Polyster Printing',
        'subtitle': _getStepDate(6),
        'description': 'Advanced design modifications',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/lamination_icon.svg',
        'title': 'Lamination',
        'subtitle': _getStepDate(7),
        'description': 'Heat treatment process in progress',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/pasting_icon.svg',
        'title': 'Metallised Pasting',
        'subtitle': _getStepDate(8),
        'description': 'Metallic coating application',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/heating_icon.svg',
        'title': 'Heating',
        'subtitle': _getStepDate(9),
        'description': 'Order ready for shipping',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/curing_icon.svg',
        'title': 'Curing',
        'subtitle': _getStepDate(10),
        'description': 'Final packaging process',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/zipper_icon.svg',
        'title': 'Zipper Addition',
        'subtitle': _getStepDate(11),
        'description': 'LD Pasting Application',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/slitting_icon.svg',
        'title': 'Slitting',
        'subtitle': _getStepDate(12),
        'description': 'Polyster printing in progress',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/pouching_icon.svg',
        'title': 'Pouching',
        'subtitle': _getStepDate(13),
        'description': 'Final packaging process',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/sorting_icon.svg',
        'title': 'Sorting',
        'subtitle': _getStepDate(14),
        'description': 'Items getting sorted and organised',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/packing_icon.svg',
        'title': 'Packing',
        'subtitle': _getStepDate(15),
        'description': 'Order items are being packed',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/readytodispatch_icon.svg',
        'title': 'Ready to Dispatch',
        'subtitle': _getStepDate(16),
        'description': 'Order ready for shipping',
        'isCompleted': false,
        'isActive': false,
        'isLast': false,
      },
      {
        'icon': 'assets/icons/dispatched_icon.svg',
        'title': 'Dispatched',
        'subtitle': _getStepDate(17),
        'description': '',
        'isCompleted': false,
        'isActive': false,
        'isLast': true,
      },
    ];

    // Determine current step based on order status
    int currentStepIndex = _getCurrentStepIndex(status);

    // Mark steps as completed, active, or pending
    for (int i = 0; i < allSteps.length; i++) {
      allSteps[i]['isCompleted'] = i < currentStepIndex;
      allSteps[i]['isActive'] = i == currentStepIndex;
      allSteps[i]['isLast'] = i == allSteps.length - 1;
    }

    return allSteps;
  }

  String _getStepDate(int stepIndex) {
    if (order == null) return '';

    final createdDate = order!.createdAt;

    // Calculate dates based on step index (adding days to creation date)
    final stepDate = createdDate.add(Duration(days: stepIndex));

    // Format the date
    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Convert to 12-hour format
    int hour12 = stepDate.hour;
    String period = 'AM';
    if (hour12 == 0) {
      hour12 = 12;
    } else if (hour12 > 12) {
      hour12 = hour12 - 12;
      period = 'PM';
    } else if (hour12 == 12) {
      period = 'PM';
    }

    return '${stepDate.day} ${months[stepDate.month - 1]} ${stepDate.year}, ${hour12.toString()}:${stepDate.minute.toString().padLeft(2, '0')} $period';
  }

  int _getCurrentStepIndex(String status) {
    // Normalize the status for comparison (lowercase, remove extra spaces)
    final normalizedStatus = status.toLowerCase().trim().replaceAll(RegExp(r'\s+'), ' ');

    log('🔍 Matching status: "$status" -> normalized: "$normalizedStatus"');

    // Define step keywords for character matching
    final stepKeywords = [
      // Step 0: On-boarding Process
      ['onboarding', 'on-boarding', 'on boarding'],
      // Step 1: Designing
      ['designing', 'design'],
      // Step 2: Sampling
      ['sampling', 'sample'],
      // Step 3: Design Plate Approval
      ['design plate approval', 'plate approval', 'design approval'],
      // Step 4: Cylinder Development
      ['cylinder development', 'cylinder', 'developement'],
      // Step 5: Polyester Sample Approved
      ['polyester sample approved', 'polyester sample approved', 'polyester approved', 'polyester approved'],
      // Step 6: Polyester Printing
      ['polyester printing', 'polyester printing', 'printing'],
      // Step 7: Lamination
      ['lamination', 'laminating'],
      // Step 8: Metallised Pasting
      ['metallised pasting', 'metalised pasting', 'pasting', 'metallized pasting'],
      // Step 9: Heating
      ['heating', 'heat'],
      // Step 10: Curing
      ['curing', 'cure'],
      // Step 11: Zipper Addition
      ['zipper addition', 'zipper', 'zip'],
      // Step 12: Slitting
      ['slitting', 'slit'],
      // Step 13: Pouching
      ['pouching', 'pouch'],
      // Step 14: Sorting
      ['sorting', 'sort'],
      // Step 15: Packing
      ['packing', 'pack'],
      // Step 16: Ready to Dispatch
      ['ready to dispatch', 'ready dispatch', 'ready'],
      // Step 17: Dispatched
      ['dispatched', 'dispatch', 'completed', 'complete']
    ];

    // Check for character matching with each step
    for (int i = stepKeywords.length - 1; i >= 0; i--) {
      for (String keyword in stepKeywords[i]) {
        if (_isStatusMatch(normalizedStatus, keyword)) {
          log('✅ Matched step $i with keyword "$keyword" -> returning step index ${i + 1}');
          return i + 1; // Return step index + 1 (since we want the next step to be active)
        }
      }
    }

    // Special handling for common status variations
    if (normalizedStatus.contains('pending') || normalizedStatus.contains('placed')) {
      log('📝 Special case: pending/placed -> returning step index 1');
      return 1; // On-boarding Process completed, Designing active
    }

    if (normalizedStatus.contains('approved') && !normalizedStatus.contains('polyster') && !normalizedStatus.contains('polyester')) {
      log('📝 Special case: approved (not polyster) -> returning step index 4');
      return 4; // Design Plate Approval completed
    }

    // Default to first step
    log('⚠️ No match found, defaulting to step index 1');
    return 1;
  }

  bool _isStatusMatch(String status, String keyword) {
    // Check if the status contains the keyword
    if (status.contains(keyword)) {
      return true;
    }

    // Check for partial character matching (at least 70% of characters match)
    return _calculateSimilarity(status, keyword) > 0.7;
  }

  double _calculateSimilarity(String status, String keyword) {
    // Simple character matching algorithm
    final statusWords = status.split(' ');
    final keywordWords = keyword.split(' ');

    int matchingWords = 0;
    for (String keywordWord in keywordWords) {
      for (String statusWord in statusWords) {
        if (statusWord.contains(keywordWord) || keywordWord.contains(statusWord)) {
          matchingWords++;
          break;
        }

        // Check character similarity for individual words
        if (_wordSimilarity(statusWord, keywordWord) > 0.8) {
          matchingWords++;
          break;
        }
      }
    }

    return matchingWords / keywordWords.length;
  }

  double _wordSimilarity(String word1, String word2) {
    if (word1.isEmpty || word2.isEmpty) return 0.0;

    int matches = 0;
    int minLength = word1.length < word2.length ? word1.length : word2.length;

    for (int i = 0; i < minLength; i++) {
      if (word1[i] == word2[i]) {
        matches++;
      }
    }

    return matches / (word1.length > word2.length ? word1.length : word2.length);
  }

  Widget _buildTrackingStep({
    required String icon,
    required String title,
    required String subtitle,
    required String description,
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: BoxDecoration(
                color: isCompleted || isActive
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SmartIcon.colored(
                  assetPath: icon,
                  color: isCompleted || isActive ? AppColors.blackColor : AppColors.greyColor,
                  size: MySize.size20,
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: MySize.size2,
                height: MySize.size40,
                color: isCompleted
                    ? AppColors.primaryColor
                    : AppColors.greyColor.withValues(alpha: 0.3),
              ),
          ],
        ),
        Space.width(12),
        // Content
        Expanded(
          child: Container(
            padding: EdgeInsets.only(bottom: MySize.size16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isActive
                            ? AppColors.blackColor
                            : AppColors.greyColor,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OrderStatusScreen(
                              orderId: widget.orderId,
                              orderDate: widget.orderDate,
                              orderImage: widget.orderImage,
                              stepTitle: title,
                            ),
                          ),
                        );
                      },
                      child: Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                Space.height(4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetails() {
    if (order == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  ClipRRect(
                    borderRadius: Shape.circular(8),
                    child: _buildOrderImage(),
                  ),
                  Space.width(12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order!.productName.isNotEmpty ? order!.productName : 'Custom Order',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.blackColor,
                          ),
                        ),
                        Space.height(4),
                        Text(
                          'Quantity: ${order!.quantity} Items',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.greyColor,
                          ),
                        ),
                        Space.height(4),
                        Text(
                          order!.formattedPrice,
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Space.height(16),
              // Additional order details
              _buildDetailRow('Order ID', order!.orderId),
              _buildDetailRow('Priority', order!.priority),
              _buildDetailRow('Payment Status', order!.payment),
              if (order!.notes.isNotEmpty) _buildDetailRow('Notes', order!.notes),
              _buildDetailRow('Delivery Date', order!.formattedDeliveryDate),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: MySize.size8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: MySize.size100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.greyColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.blackColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingAddress() {
    if (order == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Shipping Address',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                order!.customerName.isNotEmpty ? order!.customerName : 'Customer',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
              ),
              if (order!.businessName.isNotEmpty) ...[
                Space.height(4),
                Text(
                  order!.businessName,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.blackColor,
                  ),
                ),
              ],
              Space.height(8),
              Text(
                order!.businessAddress.isNotEmpty
                  ? order!.businessAddress
                  : 'Address not available',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.greyColor,
                  height: 1.4,
                ),
              ),
              if (order!.mobileNumber.isNotEmpty) ...[
                Space.height(8),
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: MySize.size16,
                      color: AppColors.greyColor,
                    ),
                    Space.width(8),
                    Text(
                      order!.mobileNumber,
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.greyColor,
                      ),
                    ),
                  ],
                ),
              ],
              if (order!.emailId.isNotEmpty) ...[
                Space.height(4),
                Row(
                  children: [
                    Icon(
                      Icons.email,
                      size: MySize.size16,
                      color: AppColors.greyColor,
                    ),
                    Space.width(8),
                    Expanded(
                      child: Text(
                        order!.emailId,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.greyColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              if (order!.gstNumber.isNotEmpty) ...[
                Space.height(8),
                Text(
                  'GST: ${order!.gstNumber}',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.greyColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}