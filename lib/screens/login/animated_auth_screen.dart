import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/providers/auth_provider.dart';
import 'package:packagingwala/screens/home/<USER>';
import 'package:packagingwala/widgets/custom_snackbar.dart';
import 'package:packagingwala/widgets/validators.dart';
import 'package:pinput/pinput.dart';
import '../../constants/app_colors.dart';
import '../../constants/size.dart';
import '../../widgets/custom_text_field.dart';

enum AuthState { splash, login, otp }

class AnimatedAuthScreen extends ConsumerStatefulWidget {
  const AnimatedAuthScreen({super.key});

  @override
  ConsumerState<AnimatedAuthScreen> createState() => _AnimatedAuthScreenState();
}

class _AnimatedAuthScreenState extends ConsumerState<AnimatedAuthScreen>
    with TickerProviderStateMixin {
  AuthState _currentState = AuthState.splash;

  // Controllers
  late AnimationController _animationController;
  late AnimationController _fadeController;
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Animations
  late Animation<Offset> _buttonSlideAnimation;
  late Animation<Offset> _loginSlideAnimation;
  late Animation<Offset> _otpSlideAnimation;
  late Animation<double> _buttonFadeAnimation;
  late Animation<double> _loginFadeAnimation;
  late Animation<double> _otpFadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Initialize slide animations
    _buttonSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 2), // Slide down
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loginSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom
      end: Offset.zero, // Move to center
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _otpSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom
      end: Offset.zero, // Move to center
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Initialize fade animations
    _buttonFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _loginFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    _otpFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fadeController.dispose();
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _onGetStartedPressed() async {
    setState(() {
      _currentState = AuthState.login;
    });

    // Start fade out animation for button
    _fadeController.forward();

    // Wait a bit then start slide animations
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  void _onNextPressed() async {
    if (!_formKey.currentState!.validate()) return;

    final phone = "+91${_phoneController.text.trim()}";

    ref
        .read(authControllerProvider.notifier)
        .loginWithPhone(
          phone,
          (verificationId, resendToken) {
            // Navigator.pushNamed(
            //   context,
            //   otpScreen,
            //   arguments: {
            //     'verificationId': verificationId,
            //     'phoneNumber': phone,
            //   },
            // );
          },
          (errorMessage) {
            customSnackBar(context, errorMessage, color: Colors.red);
          },
        );
    setState(() {
      _currentState = AuthState.otp;
    });

    // Reset animations for next transition
    _animationController.reset();
    _fadeController.reset();

    // Start fade out for login section
    _fadeController.forward();

    // Wait a bit then start slide animations
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  void _handleBackButton() {
    if (_currentState == AuthState.otp) {
      // Go back to login state
      setState(() {
        _currentState = AuthState.login;
      });

      // Reset animations
      _animationController.reset();
      _fadeController.reset();

      // Start animations
      _fadeController.forward();
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        _animationController.forward();
      });
    } else if (_currentState == AuthState.login) {
      // Go back to splash state
      setState(() {
        _currentState = AuthState.splash;
      });

      // Reset animations
      _animationController.reset();
      _fadeController.reset();

      // Start animations
      _fadeController.forward();
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        _animationController.forward();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _currentState == AuthState.splash,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackButton();
        }
      },
      child: Scaffold(
        backgroundColor:
            _currentState == AuthState.splash
                ? Colors.white
                : AppColors.backgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              // Logo Section (Always visible)
              Expanded(
                flex: 1,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo with conditional styling
                      if (_currentState == AuthState.splash) ...[
                        Container(
                          padding: EdgeInsets.all(MySize.size20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: MySize.size10,
                                offset: Offset(0, MySize.size4),
                              ),
                            ],
                          ),
                          child: Image.asset(
                            'assets/images/brand_logo.png',
                            width: MySize.size60,
                            height: MySize.size60,
                          ),
                        ),
                      ] else ...[
                        Image.asset(
                          'assets/images/brand_logo.png',
                          width: MySize.size60,
                          height: MySize.size60,
                        ),
                      ],
                      Space.height(20),
                      Image.asset(
                        'assets/images/brand_logo_text.png',
                        width: MySize.size180,
                      ),
                      if (_currentState == AuthState.splash) ...[
                        Space.height(10),
                        Text(
                          'Your Packaging Partner',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.blackColor.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Dynamic Content Section - Positioned at bottom
              Stack(
                children: [
                  // Get Started Button (Splash State)
                  if (_currentState == AuthState.splash)
                    SlideTransition(
                      position: _buttonSlideAnimation,
                      child: FadeTransition(
                        opacity: _buttonFadeAnimation,
                        child: _buildGetStartedSection(),
                      ),
                    ),

                  // Login Section
                  if (_currentState == AuthState.login)
                    SlideTransition(
                      position: _loginSlideAnimation,
                      child: FadeTransition(
                        opacity: _loginFadeAnimation,
                        child: _buildLoginSection(),
                      ),
                    ),

                  // OTP Section
                  if (_currentState == AuthState.otp)
                    SlideTransition(
                      position: _otpSlideAnimation,
                      child: FadeTransition(
                        opacity: _otpFadeAnimation,
                        child: _buildOTPSection(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGetStartedSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size40,
        vertical: MySize.size40,
      ),
      child: ElevatedButton(
        onPressed: _onGetStartedPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          padding: EdgeInsets.symmetric(vertical: MySize.size16),
          elevation: 0,
          shape: Shape.circular(30, shapeTypeFor: ShapeTypeFor.button),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Get Started',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Space.width(8),
            Icon(Icons.arrow_forward, color: Colors.black),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circularTop(32),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Content area
            Padding(
              padding: EdgeInsets.all(MySize.size24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Heading
                  Center(
                    child: Text(
                      'Login/Signup',
                      style: TextStyle(
                        fontSize: MySize.size24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                  Space.height(8),

                  // Subtitle
                  Center(
                    child: Text(
                      'Login/Signup to start managing your orders.',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  Space.height(30),

                  // Phone Number label
                  Text(
                    'Phone Number',
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  Space.height(8),

                  // Phone number field
                  CustomTextField(
                    controller: _phoneController,
                    hintText: 'Enter Your Phone Number',
                    validator: Validators.numberValidator,
                    keyboardType: TextInputType.phone,
                    borderColor: Colors.grey[300],
                    focusedBorderColor: AppColors.primaryColor,
                  ),
                ],
              ),
            ),

            // Next button at bottom
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size40,
                vertical: MySize.size40,
              ),
              child: ElevatedButton(
                onPressed: _onNextPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: MySize.size16),
                  elevation: 0,
                  shape: Shape.circular(30, shapeTypeFor: ShapeTypeFor.button),
                ),
                child: Text(
                  'Next',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOTPSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circularTop(32),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content area
          Padding(
            padding: EdgeInsets.all(MySize.size24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Heading
                Text(
                  'Verify Your Number',
                  style: TextStyle(
                    fontSize: MySize.size24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryColor,
                  ),
                ),
                Space.height(8),

                // Subtitle
                Text(
                  "We've sent an OTP to your phone number.",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Colors.grey[600],
                  ),
                ),
                Space.height(30),

                // OTP Input (Pinput)
                Pinput(
                  length: 6,
                  controller: _otpController,
                  defaultPinTheme: PinTheme(
                    width: MySize.size56,
                    height: MySize.size56,
                    textStyle: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: Shape.circular(12),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  focusedPinTheme: PinTheme(
                    width: MySize.size56,
                    height: MySize.size56,
                    textStyle: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: Shape.circular(12),
                      border: Border.all(color: AppColors.primaryColor),
                    ),
                  ),
                ),

                Space.height(10),

                // Resend code text
                Center(
                  child: TextButton(
                    onPressed: () {
                      // Resend OTP logic here
                    },
                    child: Text(
                      "Didn't receive the code? Resend",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Verify Button at bottom
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size40,
              vertical: MySize.size30,
            ),
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const HomeScreen()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding: EdgeInsets.symmetric(vertical: MySize.size16),
                elevation: 0,
                shape: Shape.circular(30, shapeTypeFor: ShapeTypeFor.button),
              ),
              child: Text(
                'Verify',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.blackColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
