class Validators {
  static String? validator(String? value, String? field) {
    if (value == null || value.isEmpty) {
      return '$field can not be empty';
    }
    return null;
  }

  static String? numberValidator(String?value){
    if(value==null||value.isEmpty){
      return 'Mobile Number can not be empty';
    }
    else if(value.length!=10){
      return 'Mobile Number should be of 10 digits';
    }
    return null;
  }

  static String? otpValidator(String?value){
    if(value==null||value.isEmpty){
      return 'OTP field can not be empty';
    }
    else if(value.length!=6){
      return 'OTP should be of 6 digits';
    }
    return null;
  }
}
