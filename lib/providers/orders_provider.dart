import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/models/order.dart';
import 'package:packagingwala/services/firestore_service.dart';
import 'package:packagingwala/providers/customer_provider.dart';

// Provider for FirestoreService
final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  return FirestoreService();
});

// State class for orders
class OrdersState {
  final List<Order> allOrders;
  final List<Order> pendingOrders;
  final List<Order> processedOrders;
  final bool isLoading;
  final String? error;

  OrdersState({
    this.allOrders = const [],
    this.pendingOrders = const [],
    this.processedOrders = const [],
    this.isLoading = false,
    this.error,
  });

  OrdersState copyWith({
    List<Order>? allOrders,
    List<Order>? pendingOrders,
    List<Order>? processedOrders,
    bool? isLoading,
    String? error,
  }) {
    return OrdersState(
      allOrders: allOrders ?? this.allOrders,
      pendingOrders: pendingOrders ?? this.pendingOrders,
      processedOrders: processedOrders ?? this.processedOrders,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

// Orders provider
class OrdersNotifier extends StateNotifier<OrdersState> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  OrdersNotifier(this._firestoreService, this._ref) : super(OrdersState());

  // Load orders for the current customer
  Future<void> loadCustomerOrders() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get current customer from customer provider
      final customerState = _ref.read(customerControllerProvider);
      final customer = customerState.customer;

      if (customer == null) {
        log('No customer found in customer state');
        state = state.copyWith(
          isLoading: false,
          error: 'Customer not found',
        );
        return;
      }

      log('Loading orders for customer: ${customer.customerId}');

      // Fetch orders from Firestore
      final orders = await _firestoreService.getOrdersByCustomerId(customer.customerId);

      // Filter orders by status
      final pendingOrders = orders.where((order) => order.isPending).toList();
      final processedOrders = orders.where((order) => order.isProcessed).toList();

      state = state.copyWith(
        allOrders: orders,
        pendingOrders: pendingOrders,
        processedOrders: processedOrders,
        isLoading: false,
      );

      log('Loaded ${orders.length} orders (${pendingOrders.length} pending, ${processedOrders.length} processed)');
    } catch (e) {
      log('Error loading customer orders: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load orders: $e',
      );
    }
  }

  // Get orders by tab index
  List<Order> getOrdersByTab(int tabIndex) {
    switch (tabIndex) {
      case 0: // All Orders
        return state.allOrders;
      case 1: // Pending Orders
        return state.pendingOrders;
      case 2: // Processed Orders
        return state.processedOrders;
      default:
        return state.allOrders;
    }
  }

  // Refresh orders
  Future<void> refreshOrders() async {
    await loadCustomerOrders();
  }

  // Get order by ID
  Future<Order?> getOrderById(String orderId) async {
    try {
      return await _firestoreService.getOrderById(orderId);
    } catch (e) {
      log('Error fetching order by ID: $e');
      return null;
    }
  }

  // Search orders
  List<Order> searchOrders(String query, int tabIndex) {
    if (query.isEmpty) {
      return getOrdersByTab(tabIndex);
    }

    final orders = getOrdersByTab(tabIndex);
    return orders.where((order) {
      return order.orderId.toLowerCase().contains(query.toLowerCase()) ||
             order.productName.toLowerCase().contains(query.toLowerCase()) ||
             order.customerName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}

// Provider for orders
final ordersProvider = StateNotifierProvider<OrdersNotifier, OrdersState>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return OrdersNotifier(firestoreService, ref);
});

// Provider for filtered orders based on search and tab
final filteredOrdersProvider = Provider.family<List<Order>, Map<String, dynamic>>((ref, params) {
  final ordersNotifier = ref.watch(ordersProvider.notifier);
  final searchQuery = params['searchQuery'] as String? ?? '';
  final tabIndex = params['tabIndex'] as int? ?? 0;

  return ordersNotifier.searchOrders(searchQuery, tabIndex);
});
