import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/models/customer.dart';
import 'package:packagingwala/services/firestore_service.dart';
import 'package:packagingwala/services/storage_service.dart';

// Customer state class
class CustomerState {
  final bool isLoading;
  final Customer? customer;
  final String? errorMessage;

  const CustomerState({
    this.isLoading = false,
    this.customer,
    this.errorMessage,
  });

  CustomerState copyWith({
    bool? isLoading,
    Customer? customer,
    String? errorMessage,
  }) {
    return CustomerState(
      isLoading: isLoading ?? this.isLoading,
      customer: customer ?? this.customer,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

final customerControllerProvider =
    StateNotifierProvider<CustomerProvider, CustomerState>((ref) {
  return CustomerProvider(FirestoreService());
});

class CustomerProvider extends StateNotifier<CustomerState> {
  final FirestoreService _firestoreService;

  CustomerProvider(this._firestoreService) : super(const CustomerState()) {
    _loadCustomerData();
  }

  /// Load customer data based on stored phone number
  Future<void> _loadCustomerData() async {
    state = state.copyWith(isLoading: true);
    
    try {
      // Get stored phone number
      final phoneNumber = StorageService.getUserPhone();
      
      if (phoneNumber != null) {
        // Try to find customer by phone number
        final customer = await _firestoreService.getCustomerByMobileNumber(phoneNumber);
        
        if (customer != null) {
          // Save customer ID to storage for future reference
          await StorageService.setString('customer_id', customer.customerId);
          
          state = state.copyWith(
            isLoading: false,
            customer: customer,
          );
        } else {
          state = state.copyWith(
            isLoading: false,
            errorMessage: 'Customer not found',
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Phone number not found',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load customer data: $e',
      );
    }
  }

  /// Refresh customer data
  Future<void> refreshCustomerData() async {
    await _loadCustomerData();
  }

  /// Get customer by ID
  Future<void> getCustomerById(String customerId) async {
    state = state.copyWith(isLoading: true);
    
    try {
      final customer = await _firestoreService.getCustomerById(customerId);
      
      if (customer != null) {
        state = state.copyWith(
          isLoading: false,
          customer: customer,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Customer not found',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load customer: $e',
      );
    }
  }

  /// Clear customer data (for logout)
  void clearCustomerData() {
    state = const CustomerState();
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Get customer name for display
  String getDisplayName() {
    if (state.customer != null) {
      return state.customer!.fullName.isNotEmpty 
          ? state.customer!.fullName 
          : state.customer!.businessName;
    }
    return 'User';
  }

  /// Get business address for display
  String getBusinessAddress() {
    if (state.customer != null && state.customer!.businessAddress.isNotEmpty) {
      return state.customer!.businessAddress;
    }
    return 'Location not available';
  }
}
