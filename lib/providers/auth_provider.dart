import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:packagingwala/services/auth_service.dart';
import 'package:packagingwala/services/storage_service.dart';

final authControllerProvider =
    StateNotifierProvider<AuthProvider, bool>((ref) {
  return AuthProvider(AuthService());
});

class AuthProvider extends StateNotifier<bool> {
  final AuthService _authService;

  AuthProvider(this._authService) : super(false);

  Future<void> loginWithPhone(
    String phoneNumber,
    void Function(String verificationId, int? resendToken) onCodeSent,
    void Function(String error)? onError,
  ) async {
    state = true;
    try {
      await _authService.verifyPhoneNumber(phoneNumber, onCodeSent);
    } on FirebaseAuthException catch (e) {
      onError?.call(e.message ?? "Login failed");
    } finally {
      state = false;
    }
  }

  Future<bool> verifyOTP(
    String verificationId,
    String otpCode,
    void Function(String error)? onError,
  ) async {
    state = true;
    try {
      final userCredential = await _authService.signInWithOTP(verificationId, otpCode);

      if (userCredential != null) {
        // Save login state and user data
        await StorageService.setLoggedIn(true);
        await StorageService.setUserUid(userCredential.user!.uid);
        if (userCredential.user!.phoneNumber != null) {
          await StorageService.setUserPhone(userCredential.user!.phoneNumber!);
        }
        return true;
      } else {
        onError?.call("Invalid OTP. Please try again.");
        return false;
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage = "OTP verification failed";

      // Handle specific Firebase Auth errors
      switch (e.code) {
        case 'invalid-verification-code':
          errorMessage = "Invalid OTP. Please check and try again.";
          break;
        case 'session-expired':
          errorMessage = "OTP has expired. Please request a new one.";
          break;
        case 'too-many-requests':
          errorMessage = "Too many attempts. Please try again later.";
          break;
        default:
          errorMessage = e.message ?? "OTP verification failed";
      }

      onError?.call(errorMessage);
      return false;
    } catch (e) {
      onError?.call("OTP verification failed");
      return false;
    } finally {
      state = false;
    }
  }

  Future<void> logout() async {
    state = true;
    try {
      await _authService.logout();
      await StorageService.clearUserData();
    } catch (e) {
      // Handle logout error if needed
    } finally {
      state = false;
    }
  }
}
