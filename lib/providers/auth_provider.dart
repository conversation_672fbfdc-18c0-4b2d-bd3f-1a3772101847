import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:packagingwala/services/auth_service.dart';

final authControllerProvider =
    StateNotifierProvider<AuthProvider, bool>((ref) {
  return AuthProvider(AuthService());
});

class AuthProvider extends StateNotifier<bool> {
  final AuthService _authService;

  AuthProvider(this._authService) : super(false);

  Future<void> loginWithPhone(
    String phoneNumber,
    void Function(String verificationId, int? resendToken) onCodeSent,
    void Function(String error)? onError,
  ) async {
    state = true;
    try {
      await _authService.verifyPhoneNumber(phoneNumber, onCodeSent);
    } on FirebaseAuthException catch (e) {
      onError?.call(e.message ?? "Login failed");
    } finally {
      state = false;
    }
  }
}
