import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:packagingwala/services/auth_service.dart';
import 'package:packagingwala/services/storage_service.dart';

// Auth state enum
enum AuthStatus { initial, loading, authenticated, unauthenticated }

// Auth state class
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.errorMessage,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

final authControllerProvider =
    StateNotifierProvider<AuthProvider, AuthState>((ref) {
  return AuthProvider(AuthService());
});

class AuthProvider extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthProvider(this._authService) : super(const AuthState(status: AuthStatus.initial)) {
    _checkAuthState();
  }

  /// Check if user is already authenticated
  Future<void> _checkAuthState() async {
    state = state.copyWith(status: AuthStatus.loading);

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      final isStoredLoggedIn = StorageService.isLoggedIn();

      if (currentUser != null && isStoredLoggedIn) {
        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: currentUser,
        );
      } else {
        state = state.copyWith(status: AuthStatus.unauthenticated);
      }
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        errorMessage: 'Failed to check authentication state',
      );
    }
  }

  Future<void> loginWithPhone(
    String phoneNumber,
    void Function(String verificationId, int? resendToken) onCodeSent,
    void Function(String error)? onError,
  ) async {
    state = state.copyWith(status: AuthStatus.loading);
    try {
      await _authService.verifyPhoneNumber(phoneNumber, onCodeSent);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        errorMessage: e.message ?? "Login failed",
      );
      onError?.call(e.message ?? "Login failed");
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        errorMessage: "Login failed",
      );
      onError?.call("Login failed");
    }
  }

  Future<bool> verifyOTP(
    String verificationId,
    String otpCode,
    void Function(String error)? onError,
  ) async {
    state = state.copyWith(status: AuthStatus.loading);
    try {
      final userCredential = await _authService.signInWithOTP(verificationId, otpCode);

      if (userCredential != null) {
        // Save login state and user data
        await StorageService.setLoggedIn(true);
        await StorageService.setUserUid(userCredential.user!.uid);
        if (userCredential.user!.phoneNumber != null) {
          await StorageService.setUserPhone(userCredential.user!.phoneNumber!);
        }

        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: userCredential.user,
        );
        return true;
      } else {
        state = state.copyWith(
          status: AuthStatus.unauthenticated,
          errorMessage: "Invalid OTP. Please try again.",
        );
        onError?.call("Invalid OTP. Please try again.");
        return false;
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage = "OTP verification failed";

      // Handle specific Firebase Auth errors
      switch (e.code) {
        case 'invalid-verification-code':
          errorMessage = "Invalid OTP. Please check and try again.";
          break;
        case 'session-expired':
          errorMessage = "OTP has expired. Please request a new one.";
          break;
        case 'too-many-requests':
          errorMessage = "Too many attempts. Please try again later.";
          break;
        default:
          errorMessage = e.message ?? "OTP verification failed";
      }

      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        errorMessage: errorMessage,
      );
      onError?.call(errorMessage);
      return false;
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        errorMessage: "OTP verification failed",
      );
      onError?.call("OTP verification failed");
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(status: AuthStatus.loading);
    try {
      await _authService.logout();
      await StorageService.clearUserData();
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        user: null,
      );
    } catch (e) {
      state = state.copyWith(
        errorMessage: "Logout failed",
      );
    }
  }

  /// Check if user is currently loading
  bool get isLoading => state.status == AuthStatus.loading;

  /// Check if user is authenticated
  bool get isAuthenticated => state.status == AuthStatus.authenticated;

  /// Get current user
  User? get currentUser => state.user;
}
