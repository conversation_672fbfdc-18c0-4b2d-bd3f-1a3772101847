import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/services/storage_service.dart';

// Splash state enum
enum SplashStatus { loading, navigateToHome, navigateToLogin }

// Splash state class
class SplashState {
  final SplashStatus status;
  final String? errorMessage;

  const SplashState({
    required this.status,
    this.errorMessage,
  });

  SplashState copyWith({
    SplashStatus? status,
    String? errorMessage,
  }) {
    return SplashState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

final splashControllerProvider =
    StateNotifierProvider<SplashProvider, SplashState>((ref) {
  return SplashProvider();
});

class SplashProvider extends StateNotifier<SplashState> {
  SplashProvider() : super(const SplashState(status: SplashStatus.loading)) {
    _checkLoginStatus();
  }

  /// Check login status and determine navigation
  Future<void> _checkLoginStatus() async {
    try {
      // Wait for a short delay to show splash screen
      await Future.delayed(const Duration(seconds: 2));
      
      // Check if user is logged in
      final isLoggedIn = StorageService.isLoggedIn();
      
      if (isLoggedIn) {
        state = state.copyWith(status: SplashStatus.navigateToHome);
      } else {
        state = state.copyWith(status: SplashStatus.navigateToLogin);
      }
    } catch (e) {
      state = state.copyWith(
        status: SplashStatus.navigateToLogin,
        errorMessage: 'Failed to check login status',
      );
    }
  }

  /// Reset state (useful for testing or manual refresh)
  void reset() {
    state = const SplashState(status: SplashStatus.loading);
    _checkLoginStatus();
  }
}
