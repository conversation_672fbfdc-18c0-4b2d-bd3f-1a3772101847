import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/services/auth_service.dart';
import 'package:packagingwala/services/storage_service.dart';
import 'package:permission_handler/permission_handler.dart';

// Profile state class
class ProfileState {
  final bool isLoading;
  final bool notificationEnabled;
  final String? userPhone;
  final String? userUid;
  final String? errorMessage;

  const ProfileState({
    this.isLoading = false,
    this.notificationEnabled = false,
    this.userPhone,
    this.userUid,
    this.errorMessage,
  });

  ProfileState copyWith({
    bool? isLoading,
    bool? notificationEnabled,
    String? userPhone,
    String? userUid,
    String? errorMessage,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      userPhone: userPhone ?? this.userPhone,
      userUid: userUid ?? this.userUid,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

final profileControllerProvider =
    StateNotifierProvider<ProfileProvider, ProfileState>((ref) {
  return ProfileProvider(AuthService());
});

class ProfileProvider extends StateNotifier<ProfileState> {
  final AuthService _authService;

  ProfileProvider(this._authService) : super(const ProfileState()) {
    _initializeProfile();
  }

  /// Initialize profile data
  Future<void> _initializeProfile() async {
    state = state.copyWith(isLoading: true);
    
    try {
      // Get user data from storage
      final userPhone = StorageService.getUserPhone();
      final userUid = StorageService.getUserUid();
      
      // Check notification permission
      final notificationStatus = await Permission.notification.status;
      
      state = state.copyWith(
        isLoading: false,
        userPhone: userPhone,
        userUid: userUid,
        notificationEnabled: notificationStatus.isGranted,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load profile data',
      );
    }
  }

  /// Toggle notification permission
  Future<void> toggleNotification(bool value) async {
    if (value) {
      final status = await Permission.notification.request();
      state = state.copyWith(notificationEnabled: status.isGranted);
    } else {
      // Can't programmatically disable notifications
      // User needs to go to settings manually
      state = state.copyWith(notificationEnabled: false);
    }
  }

  /// Logout user
  Future<bool> logout() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await _authService.logout();
      await StorageService.clearUserData();
      
      state = state.copyWith(
        isLoading: false,
        userPhone: null,
        userUid: null,
        notificationEnabled: false,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Logout failed. Please try again.',
      );
      return false;
    }
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    await _initializeProfile();
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}
