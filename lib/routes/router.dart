import 'package:flutter/material.dart';
import 'package:packagingwala/routes/route_constants.dart';
import 'package:packagingwala/screens/login/animated_auth_screen.dart';

Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    case authScreen:
      return MaterialPageRoute(builder: (_) => const AnimatedAuthScreen());

    default:
      return MaterialPageRoute(
        builder:
            (_) =>
                const Scaffold(body: Center(child: Text('No route defined'))),
      );
  }
}
