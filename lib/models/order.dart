import 'package:intl/intl.dart';

class Order {
  final String orderId;
  final String customerId;
  final String customerName;
  final String businessAddress;
  final String businessName;
  final DateTime createdAt;
  final DateTime deliveryDate;
  final String emailId;
  final String gstNumber;
  final String mobileNumber;
  final String notes;
  final String orderStatus;
  final String payment;
  final double price;
  final String priority;
  final String? productImagePath;
  final String? productImageUrl;
  final String productName;
  final int quantity;
  final DateTime updatedAt;

  Order({
    required this.orderId,
    required this.customerId,
    required this.customerName,
    required this.businessAddress,
    required this.businessName,
    required this.createdAt,
    required this.deliveryDate,
    required this.emailId,
    required this.gstNumber,
    required this.mobileNumber,
    required this.notes,
    required this.orderStatus,
    required this.payment,
    required this.price,
    required this.priority,
    this.productImagePath,
    this.productImageUrl,
    required this.productName,
    required this.quantity,
    required this.updatedAt,
  });

  factory Order.fromFirestore(Map<String, dynamic> data) {
    return Order(
      orderId: data['orderId'] ?? '',
      customerId: data['customerId'] ?? '',
      customerName: data['customerName'] ?? '',
      businessAddress: data['businessAddress'] ?? '',
      businessName: data['businessName'] ?? '',
      createdAt: _parseTimestamp(data['createdAt']),
      deliveryDate: _parseTimestamp(data['deliveryDate']),
      emailId: data['emailId'] ?? '',
      gstNumber: data['gstNumber'] ?? '',
      mobileNumber: data['mobileNumber'] ?? '',
      notes: data['notes'] ?? '',
      orderStatus: data['orderStatus'] ?? '',
      payment: data['payment'] ?? '',
      price: _parseDouble(data['price']),
      priority: data['priority'] ?? '',
      productImagePath: data['productImagePath'],
      productImageUrl: data['productImageUrl'],
      productName: data['productName'] ?? '',
      quantity: _parseInt(data['quantity']),
      updatedAt: _parseTimestamp(data['updatedAt']),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'orderId': orderId,
      'customerId': customerId,
      'customerName': customerName,
      'businessAddress': businessAddress,
      'businessName': businessName,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'deliveryDate': deliveryDate.millisecondsSinceEpoch,
      'emailId': emailId,
      'gstNumber': gstNumber,
      'mobileNumber': mobileNumber,
      'notes': notes,
      'orderStatus': orderStatus,
      'payment': payment,
      'price': price,
      'priority': priority,
      'productImagePath': productImagePath,
      'productImageUrl': productImageUrl,
      'productName': productName,
      'quantity': quantity,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Helper methods for parsing data
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();
    
    if (timestamp is int) {
      // Handle milliseconds timestamp
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp is String) {
      // Handle string timestamp
      return DateTime.tryParse(timestamp) ?? DateTime.now();
    }
    
    return DateTime.now();
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  // Helper methods for display
  String get formattedCreatedDate {
    return DateFormat('MMM d, yyyy').format(createdAt);
  }

  String get formattedDeliveryDate {
    return DateFormat('MMM d, yyyy').format(deliveryDate);
  }

  String get formattedPrice {
    return '₹${price.toStringAsFixed(0)}';
  }

  String get displayStatus {
    switch (orderStatus.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'approved':
        return 'Approved';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return orderStatus;
    }
  }

  bool get isPending {
    return orderStatus.toLowerCase().contains('pending');
  }

  bool get isProcessed {
    return orderStatus.toLowerCase().contains('approved') || 
           orderStatus.toLowerCase().contains('completed');
  }

  bool get hasImage {
    return (productImagePath != null && productImagePath!.isNotEmpty) ||
           (productImageUrl != null && productImageUrl!.isNotEmpty);
  }

  Order copyWith({
    String? orderId,
    String? customerId,
    String? customerName,
    String? businessAddress,
    String? businessName,
    DateTime? createdAt,
    DateTime? deliveryDate,
    String? emailId,
    String? gstNumber,
    String? mobileNumber,
    String? notes,
    String? orderStatus,
    String? payment,
    double? price,
    String? priority,
    String? productImagePath,
    String? productImageUrl,
    String? productName,
    int? quantity,
    DateTime? updatedAt,
  }) {
    return Order(
      orderId: orderId ?? this.orderId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      businessAddress: businessAddress ?? this.businessAddress,
      businessName: businessName ?? this.businessName,
      createdAt: createdAt ?? this.createdAt,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      emailId: emailId ?? this.emailId,
      gstNumber: gstNumber ?? this.gstNumber,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      notes: notes ?? this.notes,
      orderStatus: orderStatus ?? this.orderStatus,
      payment: payment ?? this.payment,
      price: price ?? this.price,
      priority: priority ?? this.priority,
      productImagePath: productImagePath ?? this.productImagePath,
      productImageUrl: productImageUrl ?? this.productImageUrl,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
