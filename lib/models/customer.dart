class Customer {
  final String customerId;
  final String fullName;
  final String businessName;
  final String businessAddress;
  final String emailId;
  final String mobileNumber;
  final String gstNumber;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Customer({
    required this.customerId,
    required this.fullName,
    required this.businessName,
    required this.businessAddress,
    required this.emailId,
    required this.mobileNumber,
    required this.gstNumber,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Customer.fromFirestore(Map<String, dynamic> data) {
    return Customer(
      customerId: data['customerId'] ?? '',
      fullName: data['fullName'] ?? '',
      businessName: data['businessName'] ?? '',
      businessAddress: data['businessAddress'] ?? '',
      emailId: data['emailId'] ?? '',
      mobileNumber: data['mobileNumber'] ?? '',
      gstNumber: data['gstNumber'] ?? '',
      notes: data['notes'] ?? '',
      createdAt: DateTime.tryParse(data['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(data['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'customerId': customerId,
      'fullName': fullName,
      'businessName': businessName,
      'businessAddress': businessAddress,
      'emailId': emailId,
      'mobileNumber': mobileNumber,
      'gstNumber': gstNumber,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Customer copyWith({
    String? customerId,
    String? fullName,
    String? businessName,
    String? businessAddress,
    String? emailId,
    String? mobileNumber,
    String? gstNumber,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Customer(
      customerId: customerId ?? this.customerId,
      fullName: fullName ?? this.fullName,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      emailId: emailId ?? this.emailId,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      gstNumber: gstNumber ?? this.gstNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
