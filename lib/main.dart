import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:packagingwala/firebase_options.dart';
import 'package:packagingwala/screens/splash/splash_screen.dart';
import 'package:packagingwala/services/storage_service.dart';
import 'constants/app_colors.dart';

late ProviderContainer globalProviderContainer;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Storage Service
  await StorageService.init();

  runApp(ProviderScope(child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    globalProviderContainer = ProviderScope.containerOf(context);

    MySize().init(context);
    SizeConfig().init(context);

    return MaterialApp(
      title: 'Packaging Wala',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: AppColors.primaryColor,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primaryColor,
          primary: AppColors.primaryColor,
        ),
        useMaterial3: true,
      ),
      home: const SplashScreen(),
    );
  }
}
